<template>
  <nav class="site-navbar" :class="'site-navbar--' + navbarLayoutType">
    <div class="site-navbar__header">
      <h1 class="site-navbar__brand" @click="$router.push({ name: 'home' })" style="text-align: left;padding: 0 20px;">
        <a class="site-navbar__brand-lg" href="javascript:;" style="font-size: 17px">工作服务平台</a>
        <a class="site-navbar__brand-mini" href="javascript:;">后台</a>
      </h1>
    </div>
    <div class="site-navbar__body clearfix">
      <el-menu
          class="site-navbar__menu"
          mode="horizontal">
        <el-menu-item class="site-navbar__switch" index="0" @click="sidebarFold = !sidebarFold">
          <icon-svg name="zhedie"></icon-svg>
        </el-menu-item>
      </el-menu>
      <el-menu
          class="site-navbar__menu site-navbar__menu--right"
          mode="horizontal">
        <el-menu-item class="site-navbar__avatar" index="1" v-show="managerCapacityName">
          <el-tag size="large" type="success" v-if="managerCapacity === 'ASSOCIATION_ADMIN'">{{managerCapacityName}}</el-tag>
          <el-tag size="large" type="warning" v-else-if="managerCapacity === 'SUB_ASSOCIATION_ADMIN'">{{managerCapacityName}}</el-tag>
          <el-tag size="large" type="danger" v-else-if="managerCapacity === 'COMMUNITY_ADMIN'">{{managerCapacityName}}</el-tag>
          <el-tag size="large" v-else>{{managerCapacityName}}</el-tag>
          <el-dropdown :show-timeout="0" placement="bottom" v-show="capacityList.length>0">
            <span class="el-dropdown-link" style="margin: 10px">
              切换身份
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in capacityList" :key="item.id" @click.native="switchCapacity(item.id)">
                {{ item.capacityFullName }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-menu-item>
        <el-menu-item class="site-navbar__avatar" index="2">
          <el-dropdown :show-timeout="0" placement="bottom">
            <span class="el-dropdown-link">
              <img src="~@/assets/img/avatar.png" :alt="nickName">{{ nickName }}
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="updatePasswordHandle(false)">修改密码</el-dropdown-item>
              <el-dropdown-item @click.native="logoutHandle()">退出</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-menu-item>
      </el-menu>
    </div>
    <!-- 弹窗, 修改密码 -->
    <update-password v-if="updatePassowrdVisible" ref="updatePassowrd"></update-password>
    <!-- 弹窗, 强制修改密码 -->
    <force-update-pwd v-if="forceUpdatePwdVisible" ref="forceUpdatePwd"></force-update-pwd>
    <!-- 弹窗, 团队信息完善 -->
    <team-info-perfect v-if="teamInfoPerfectVisible" ref="teamInfoPerfect" @refreshDataList="handleTeamInfoPerfected"></team-info-perfect>
  </nav>
</template>

<script>
import UpdatePassword from './main-navbar-update-password'
import ForceUpdatePwd from './main-navbar-update-force-password'
import TeamInfoPerfect from './main-navbar-team-perfect'

import {clearLoginInfo} from '@/utils'

export default {
  data() {
    return {
      updatePassowrdVisible: false,
      forceUpdatePwdVisible: false,
      capacityList: [],
      teamInfoPerfectVisible: false,
      needPerfectTeams: [], // 需要完善信息的团队列表
      currentPerfectTeamIndex: 0 // 当前正在完善的团队索引
    }
  },
  components: {
    UpdatePassword,
    ForceUpdatePwd,
    TeamInfoPerfect
  },
  mounted() {
    this.loadingCapacityList()
    this.getForceUpdatePwd()
  },
  computed: {
    navbarLayoutType: {
      get() {
        return this.$store.state.common.navbarLayoutType
      }
    },
    sidebarFold: {
      get() {
        return this.$store.state.common.sidebarFold
      },
      set(val) {
        this.$store.commit('common/updateSidebarFold', val)
      }
    },
    mainTabs: {
      get() {
        return this.$store.state.common.mainTabs
      },
      set(val) {
        this.$store.commit('common/updateMainTabs', val)
      }
    },
    userName: {
      get() {
        return this.$store.state.user.name
      }
    },
    nickName: {
      get() {
        return this.$store.state.user.nickName
      }
    },
    managerCapacityName: {
      get() {
        return this.$store.state.user.managerCapacityName
      }
    },
    managerCapacity: {
      get() {
        return this.$store.state.user.managerCapacity
      }
    }
  },
  methods: {
    // 加载身份列表
    loadingCapacityList() {
      this.$http({
        url: this.$http.adornUrl(`/admin/user/capacityList`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.capacityList = data.obj
          // 检查团队完善状态
          this.checkTeamPerfectStatus(data.obj)
        }
      })
    },
    //获取是否强制修改密码
    getForceUpdatePwd() {
      this.$http({
        url: this.$http.adornUrl(`/admin/user/getIsForceChangePassword`),
        method: 'get',
        params: this.$http.adornParams({
          'userName': this.$store.state.user.name
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          if (data.obj) {
            this.forceUpdatePwdHandle()
          } else {
            this.getOver90DaysNotChangePwd()
          }
        }
      })
    },
    //获取是否强制修改密码
    getOver90DaysNotChangePwd() {
      this.$http({
        url: this.$http.adornUrl(`/admin/user/getOver90DaysNotChangePassword`),
        method: 'get',
        params: this.$http.adornParams({
          'userName': this.$store.state.user.name
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          if (data.obj) {
            this.updatePasswordHandle(true)
          }
        }
      })
    },
    // 切换身份
    switchCapacity(id) {
      this.$confirm(`确定要切换身份吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/admin/user/refreshUserCapacity`),
          method: 'get',
          params: this.$http.adornParams(
              {expandId: id}
          )
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '切换成功,即将刷新页面',
              type: 'success',
              duration: 2500,
              onClose: () => {
                window.location.reload()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 修改密码
    updatePasswordHandle(over90DaysNotChangePwd) {
      this.updatePassowrdVisible = true
      this.$nextTick(() => {
        this.$refs.updatePassowrd.init(this.$store.state.user.name, true, over90DaysNotChangePwd)
      })
    },
    //强制修改密码
    forceUpdatePwdHandle() {
      this.forceUpdatePwdVisible = true
      this.$nextTick(() => {
        this.$refs.forceUpdatePwd.init(this.$store.state.user.name, true)
      })
    },
    // 退出
    logoutHandle() {
      this.$confirm(`确定进行[退出]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/fykj/logout`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          clearLoginInfo()
          this.$router.push({name: 'login'})
        })
      }).catch(() => {
      })
    },
    // 检查团队完善状态
    checkTeamPerfectStatus(list) {
      if (!list || !Array.isArray(list)) {
        return
      }

      // 筛选出需要完善信息的团队
      this.needPerfectTeams = list.filter(obj => {
        return obj.roleCode === 'TEAM_ADMIN' &&
               obj.teamId &&
               obj.teamNeedPerfectFlag === true
      })

      if (this.needPerfectTeams.length > 0) {
        this.currentPerfectTeamIndex = 0
        this.showTeamPerfectPrompt()
      }
    },
    // 显示团队信息完善提示
    showTeamPerfectPrompt() {
      if (this.currentPerfectTeamIndex >= this.needPerfectTeams.length) {
        // 所有团队信息已完善
        return
      }

      const currentTeam = this.needPerfectTeams[this.currentPerfectTeamIndex]
      this.$alert('您的团队信息不全，需要完善，请通过团队管理-信息变更提交完善信息', '团队信息完善提醒', {
        confirmButtonText: '立即完善',
        type: 'warning',
        closeOnClickModal: false,
        closeOnPressEscape: false,
        showClose: false
      }).then(() => {
        this.openTeamInfoPerfectDialog(currentTeam.teamId)
      })
    },
    // 打开团队信息完善弹窗
    openTeamInfoPerfectDialog(teamId) {
      this.teamInfoPerfectVisible = true
      this.$nextTick(() => {
        this.$refs.teamInfoPerfect.init(teamId)
      })
    },
    // 处理团队信息完善完成
    handleTeamInfoPerfected() {
      this.teamInfoPerfectVisible = false
      this.currentPerfectTeamIndex++

      // 继续处理下一个需要完善的团队
      if (this.currentPerfectTeamIndex < this.needPerfectTeams.length) {
        this.showTeamPerfectPrompt()
      }
    }
  }
}
</script>
